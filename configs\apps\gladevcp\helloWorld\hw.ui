<?xml version="1.0"?>
<interface>
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-requires gladevcp 0.0 -->
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <property name="default_width">200</property>
    <property name="default_height">150</property>
    <child>
      <object class="GtkVBox" id="vbox1">
        <property name="visible">True</property>
        <child>
          <object class="HAL_LED" id="hal_led1">
            <property name="visible">True</property>
            <property name="led_shape">2</property>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="HAL_ToggleButton" id="hal_togglebutton1">
            <property name="label" translatable="yes">togglebutton</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
          </object>
          <packing>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="HAL_Label" id="hal_label1">
            <property name="visible">True</property>
            <property name="ypad">7</property>
            <property name="label" translatable="yes">label</property>
          </object>
          <packing>
            <property name="position">2</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>

<?xml version="1.0"?>
<interface>
  <!-- interface-requires gladevcp 0.0 -->
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <child>
      <object class="GtkVBox" id="vbox1">
        <property name="visible">True</property>
        <child>
          <object class="HAL_SpinButton" id="spin">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="invisible_char">&#x25CF;</property>
            <property name="adjustment">adjustment2</property>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="HAL_CheckButton" id="check">
            <property name="label" translatable="yes">checkbutton</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">False</property>
            <property name="draw_indicator">True</property>
          </object>
          <packing>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="HAL_ToggleButton" id="toggle">
            <property name="label" translatable="yes">togglebutton</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
          </object>
          <packing>
            <property name="position">2</property>
          </packing>
        </child>
        <child>
          <object class="HAL_HScale" id="scale">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="adjustment">adjustment1</property>
          </object>
          <packing>
            <property name="position">3</property>
          </packing>
        </child>
        <child>
          <object class="HAL_Button" id="hal_button1">
            <property name="label" translatable="yes">run O-word
     sub</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
            <property name="related_action">hal_action_mdi1</property>
          </object>
          <packing>
            <property name="position">4</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
  </object>
  <object class="GtkAdjustment" id="adjustment2">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
  </object>
  <object class="EMC_Action_MDI" id="hal_action_mdi1">
    <property name="command">O&lt;oword&gt; call [${spin-f}] [${check}] [${toggle}] [${scale}]</property>
  </object>
</interface>

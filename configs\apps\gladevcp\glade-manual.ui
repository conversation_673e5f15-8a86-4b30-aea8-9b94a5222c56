<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.38.2 -->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <requires lib="gladevcp" version="0.0"/>
  <object class="GtkWindow" id="window1">
    <property name="can-focus">False</property>
    <child>
      <object class="HAL_Table">
        <property name="visible">True</property>
        <property name="can-focus">False</property>
        <child>
          <object class="HAL_LED" id="hal_led1">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <property name="led-blink-rate">0</property>
          </object>
        </child>
        <child>
          <object class="HAL_Label" id="hal_label1">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <property name="label" translatable="yes">label</property>
          </object>
          <packing>
            <property name="left-attach">1</property>
            <property name="right-attach">2</property>
          </packing>
        </child>
        <child>
          <object class="HAL_LED" id="hal_led2">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <property name="led-blink-rate">0</property>
          </object>
          <packing>
            <property name="top-attach">1</property>
            <property name="bottom-attach">2</property>
          </packing>
        </child>
        <child>
          <object class="HAL_HScale" id="hal_hscale1">
            <property name="visible">True</property>
            <property name="can-focus">True</property>
          </object>
          <packing>
            <property name="left-attach">1</property>
            <property name="right-attach">2</property>
            <property name="top-attach">1</property>
            <property name="bottom-attach">2</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>

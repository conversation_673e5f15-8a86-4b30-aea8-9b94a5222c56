############################ Step Generators #########################################

# Stepper module, four step generators, all using step/dir
loadrt stepgen step_type=0,0,0,0

# Load realtime ddt for velocity calculations used for CHL
loadrt ddt count=2

# Hook functions to base thread (high speed thread for step generation)
addf stepgen.make-pulses base-thread

# Hook functions to servo thread
addf stepgen.capture-position servo-thread
addf motion-command-handler servo-thread
addf motion-controller servo-thread
addf stepgen.update-freq servo-thread
addf ddt.0 servo-thread
addf ddt.1 servo-thread

# Configure signals for Z axis to be twice the size of the signals for other axis because
# Z axis has a slo-syn driver which can't handle smaller step sizes
setp stepgen.2.steplen 35200
setp stepgen.2.stepspace 35200
setp stepgen.2.dirsetup 35200
setp stepgen.2.dirhold 35200

# Set stepgen module scaling - get values from ini file
setp stepgen.0.position-scale [AXIS_0]SCALE
setp stepgen.1.position-scale [AXIS_1]SCALE
setp stepgen.2.position-scale [AXIS_2]SCALE
setp stepgen.3.position-scale [AXIS_3]SCALE

# Set stepgen module accel limits - get values from ini file
setp stepgen.0.maxaccel [AXIS_0]STEPGEN_MAXACCEL
setp stepgen.1.maxaccel [AXIS_1]STEPGEN_MAXACCEL
setp stepgen.2.maxaccel [AXIS_2]STEPGEN_MAXACCEL
setp stepgen.3.maxaccel [AXIS_3]STEPGEN_MAXACCEL

# Hook up stepgen to motion modules
net PosX-cmd joint.0.motor-pos-cmd => stepgen.0.position-cmd
net PosX-fb stepgen.0.position-fb => joint.0.motor-pos-fb ddt.0.in
net PosY-cmd joint.1.motor-pos-cmd => stepgen.1.position-cmd
net PosY-fb stepgen.1.position-fb => joint.1.motor-pos-fb ddt.1.in
# Z Axis Position and feedback signals handled by THC, see thc.hal
#net PosZ-cmd joint.2.motor-pos-cmd => stepgen.2.position-cmd
#net PosZ-fb stepgen.2.position-fb => joint.2.motor-pos-fb
net PosA-cmd joint.3.motor-pos-cmd => stepgen.3.position-cmd
net PosA-fb stepgen.3.position-fb => joint.3.motor-pos-fb

# Hook up enable signals for step generators
net EnableX joint.0.amp-enable-out => stepgen.0.enable
net EnableY joint.1.amp-enable-out => stepgen.1.enable
net EnableZ joint.2.amp-enable-out => stepgen.2.enable
net EnableA joint.3.amp-enable-out => stepgen.3.enable

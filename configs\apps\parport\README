Parport Test using pyvcp

parport_0x278  test port 0x278 < parallel port 1 (not typical)
parport_0x378  test port 0x378 < parallel port 0

To find the onboard parallel port address open a terminal and type in:

cat /proc/ioports | grep parport

usually it will be parport0

0378-037a : parport0

To find out the address of a PCI parallel port type in:

lspci -v

Note: For simulator, a 'root access required' message may be displayed.
Using root is not recommended for running linuxcnc.

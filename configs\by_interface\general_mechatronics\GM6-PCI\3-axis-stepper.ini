###############################################################################
# General section
###############################################################################

[EMC]
#- Version of this INI file
VERSION = 1.1
#+ Name of machine, for use with display, etc.
MACHINE =               GM6-PCI-3-axis-stepper
#+ Debug level, 0 means no messages. See src/emc/nml_int/emcglb.h for others
DEBUG = 0
###############################################################################
# Sections for display options
###############################################################################

[DISPLAY]
#+ Name of display program, e.g., axis
DISPLAY =              axis
# DISPLAY = 		 tklinuxcnc
# DISPLAY =              touchy
# Cycle time, in seconds, that display will sleep between polls
CYCLE_TIME =            0.200
#- Path to help file
HELP_FILE =             tklinuxcnc.txt
#- Initial display setting for position, RELATIVE or MACHINE
POSITION_OFFSET =       RELATIVE
#- Initial display setting for position, COMMANDED or ACTUAL
POSITION_FEEDBACK =     ACTUAL
#+ Highest value that will be allowed for feed override, 1.0 = 100%
MAX_FEED_OVERRIDE =     1.2
#- Prefix to be used
#PROGRAM_PREFIX = /home/<USER>/emc2/nc_files
PROGRAM_PREFIX = ../../nc_files/
#- Introductory graphic
INTRO_GRAPHIC =         linuxcnc.gif
INTRO_TIME =            1
###############################################################################
# Task controller section
###############################################################################

[FILTER]
#No Content

[RS274NGC]
#- File containing interpreter variables
PARAMETER_FILE =        gm.var
###############################################################################
# Motion control section
###############################################################################

[EMCMOT]
#- Name of the motion controller to use (only one exists for nontrivkins)
EMCMOT =              motmod
#- Timeout for comm to emcmot, in seconds
COMM_TIMEOUT =          1.0
#- Servo task period, in nanosecs - will be rounded to an int multiple of BASE_PERIOD
SERVO_PERIOD =               1000000
###############################################################################
# Hardware Abstraction Layer section
###############################################################################

[TASK]
# Name of task controller program, e.g., milltask
TASK =                  milltask
#- Cycle time, in seconds, that task controller will sleep between polls
CYCLE_TIME =            0.010

[HAL]
# list of hal config files to run through halcmd
#+ files are executed in the order in which they appear
HALFILE =                    3-axis-stepper.hal
###############################################################################
# Trajectory planner section
###############################################################################

[HALUI]
#No Content

[TRAJ]
#+ machine specific settings
COORDINATES =           X Y Z
HOME =                  0 0 0
LINEAR_UNITS =          mm
ANGULAR_UNITS =         degree
DEFAULT_LINEAR_VELOCITY = 0.424
MAX_LINEAR_VELOCITY = 30.48
DEFAULT_LINEAR_ACCELERATION = 300.0
MAX_LINEAR_ACCELERATION = 500.0
###############################################################################
# Axes sections
###############################################################################
#+ First axis

[KINS]
KINEMATICS =  trivkins
JOINTS = 3

[AXIS_X]
MIN_LIMIT = -191.0
MAX_LIMIT = 0.0
MAX_VELOCITY = 30.48
MAX_ACCELERATION = 50.0

[JOINT_0]
TYPE =                          LINEAR
HOME =                          -50.000
MAX_VELOCITY =                  30.48
STEPGEN_MAXVEL =              	0.0
MAX_ACCELERATION =              50.0
STEPGEN_MAXACCEL =              0.0
BACKLASH = 			0.000
SCALE =                   	1000
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     -191.0
MAX_LIMIT =                     0.0
FERROR = 			1.270
MIN_FERROR = 			0.254
HOME_OFFSET =                   0.0
HOME_SEARCH_VEL =               15.0
HOME_LATCH_VEL =                -2.0
HOME_USE_INDEX =                NO
HOME_IGNORE_LIMITS =            NO
HOME_SEQUENCE =                 1
#+ Second axis

[AXIS_Y]
MIN_LIMIT = -250.0
MAX_LIMIT = 0.0
MAX_VELOCITY = 30.48
MAX_ACCELERATION = 50.0

[JOINT_1]
TYPE =                          LINEAR
HOME =                          -40.000
MAX_VELOCITY =                  30.48
STEPGEN_MAXVEL =              	0.0
MAX_ACCELERATION =              50.0
STEPGEN_MAXACCEL =              0.0
BACKLASH = 			0.000
SCALE =                   	-1000
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     -250.0
MAX_LIMIT =                     0.0
FERROR = 			1.270
MIN_FERROR = 			0.254
HOME_OFFSET =                   0.0
HOME_SEARCH_VEL =               15.0
HOME_LATCH_VEL =                -3.0
HOME_USE_INDEX =                NO
HOME_IGNORE_LIMITS =            NO
HOME_SEQUENCE =                 1
#+ Third axis

[AXIS_Z]
MIN_LIMIT = -985.0
MAX_LIMIT = 0.00
MAX_VELOCITY = 30.48
MAX_ACCELERATION = 50.0

[JOINT_2]
TYPE =                          LINEAR
HOME =                          -30.0
MAX_VELOCITY =                  30.48
STEPGEN_MAXVEL =              	0.0
MAX_ACCELERATION =              50.0
STEPGEN_MAXACCEL =              0.0
BACKLASH = 			0.000
SCALE =                   	100
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     -985.0
MAX_LIMIT =                     0.00
FERROR = 			1.270
MIN_FERROR = 			0.254
HOME_OFFSET =                   0.0
HOME_SEARCH_VEL =               15.0
HOME_LATCH_VEL =                -3.0
HOME_USE_INDEX =                NO
HOME_IGNORE_LIMITS =            NO
HOME_SEQUENCE =                 0
###############################################################################
# section for main IO controller parameters
###############################################################################

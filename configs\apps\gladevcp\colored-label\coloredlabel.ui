<?xml version="1.0"?>
<interface>
  <!-- interface-requires gladevcp 0.0 -->
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <property name="default_width">200</property>
    <property name="default_height">200</property>
    <child>
      <object class="GtkVBox" id="vbox1">
        <property name="visible">True</property>
        <child>
          <object class="GtkEventBox" id="eventbox1">
            <property name="visible">True</property>
            <child>
              <object class="HAL_Label" id="hal_label1">
                <property name="visible">True</property>
                <property name="label" translatable="yes">label</property>
                <signal name="hal_pin_changed" handler="on_label_hal_pin_change"/>
              </object>
            </child>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="HAL_SpinButton" id="hal_spinbutton1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="invisible_char">&#x25CF;</property>
            <property name="adjustment">adjustment1</property>
          </object>
          <packing>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <placeholder/>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="lower">-100</property>
    <property name="upper">100</property>
    <property name="step_increment">1</property>
  </object>
</interface>

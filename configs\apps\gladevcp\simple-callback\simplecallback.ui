<?xml version="1.0"?>
<interface>
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-requires gladevcp 0.0 -->
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <property name="default_width">200</property>
    <property name="default_height">150</property>
    <child>
      <object class="GtkVBox" id="vbox1">
        <property name="visible">True</property>
        <child>
          <object class="HAL_Button" id="hal_button1">
            <property name="label" translatable="yes">Press me!</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
            <signal name="pressed" handler="on_button_press"/>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="label1">
            <property name="visible">True</property>
            <property name="label" translatable="yes">label</property>
          </object>
          <packing>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="HAL_HBar" id="hal_hbar1">
            <property name="visible">True</property>
            <property name="z1_color">#ffffffff0000</property>
            <property name="z2_color">#ffff00000000</property>
            <property name="z0_border">0.60000002384185791</property>
            <property name="max">10</property>
            <property name="bg_color">#bebebebebebe</property>
            <property name="z0_color">#0000ffff0000</property>
            <property name="z1_border">0.80000001192092896</property>
          </object>
          <packing>
            <property name="position">2</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>

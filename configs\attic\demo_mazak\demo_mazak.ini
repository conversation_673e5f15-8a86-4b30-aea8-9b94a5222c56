# mazak_rf.ini EMC controller parameters for <PERSON>'s <PERSON><PERSON> mill.
# General note: Comments can either be preceded with a # or ; - either is
# acceptable, although # is in keeping with most linux config files.
# General section -------------------------------------------------------------

[EMC]
# Version of this INI file
VERSION = 1.1
# Name of machine, for use with display, etc.
MACHINE =               LinuxCNC-MAZAK-RF
# Debug level, 0 means no messages. See src/emc/nml_int/emcglb.h for others
# DEBUG =                 0x00000003
# DEBUG =               0x00000007
DEBUG = 0
# enable adaptive feed input, we use it for feedhold button and to
# prevent motion during spindle accel, gear changes, etc
RS274NGC_STARTUP_CODE = M52 P1
# Sections for display options ------------------------------------------------

[DISPLAY]
# Name of display program, e.g., xlinuxcnc
DISPLAY =               axis
# DISPLAY =               tklinuxcnc
# DISPLAY =               mini
# Cycle time, in seconds, that display will sleep between polls
CYCLE_TIME =            0.100
# Path to help file
HELP_FILE =             tklinuxcnc.txt
# Initial display setting for position, RELATIVE or MACHINE
POSITION_OFFSET =       RELATIVE
# Initial display setting for position, COMMANDED or ACTUAL
POSITION_FEEDBACK =     ACTUAL
# Highest value that will be allowed for feed override, 1.0 = 100%
MAX_FEED_OVERRIDE =     1.2
# Prefix to be used
PROGRAM_PREFIX = ../../nc_files/
# side panel with some extra controls
PYVCP=panel.xml
# Introductory graphic
INTRO_GRAPHIC = linuxcnc.gif
INTRO_TIME = 1
#EDITOR = geany
INCREMENTS = 0.010 0.001 0.0001
# Task controller section -----------------------------------------------------

[FILTER]
#No Content

[RS274NGC]
# File containing interpreter variables
PARAMETER_FILE =        demo_mazak.var
# Motion control section ------------------------------------------------------

[EMCMOT]
EMCMOT =                motmod
# Timeout for comm to emcmot, in seconds
COMM_TIMEOUT =          1.0
# Base task period, in nanoseconds - this is the fastest thread in the machine
BASE_PERIOD =                100000
# Servo task period, in nanoseconds - will be rounded to an integer multiple
#   of BASE_PERIOD
SERVO_PERIOD =               500000
# Hardware Abstraction Layer section --------------------------------------------------

[TASK]
# Name of task controller program, e.g., milltask
TASK =                  milltask
# Cycle time, in seconds, that task controller will sleep between polls
CYCLE_TIME =            0.010
# Part program interpreter section --------------------------------------------

[HAL]
# The run script first uses halcmd to execute any HALFILE
# files, and then to execute any individual HALCMD commands.
# list of hal config files to run through halcmd
# files are executed in the order in which they appear
HALFILE =                    demo_mazak.hal
HALFILE =                    tester.hal
# list of halcmd commands to execute
# commands are executed in the order in which they appear
#HALCMD =                    save neta
# Single file that is executed after the GUI has started.
# used for "control panel" stuff, since the panel widgets
# don't exist until the GUI starts
POSTGUI_HALFILE = panel.hal
# Spindle Controller Section --------------------------------------------------

[HALUI]
#No Content

[TRAJ]
# COORDINATES =         X Y Z A B C
COORDINATES =           X Y Z
HOME =                  0 0 0
LINEAR_UNITS =          inch
ANGULAR_UNITS =         degree
DEFAULT_LINEAR_VELOCITY = 1.0
MAX_LINEAR_VELOCITY = 5.0
DEFAULT_LINEAR_ACCELERATION = 20.0
MAX_LINEAR_ACCELERATION = 20.0
# Axes sections ---------------------------------------------------------------
# First axis

[EMCIO]
# change tools in the upper left corner of the table
# to avoid hitting the vice or workpiece with the tool
TOOL_CHANGE_POSITION = -13.75 7.5 -0.022
# tool table file
TOOL_TABLE =    demo_mazak.tbl
# tool turret info
TOOL_TURRET_MAX = 24
TOOL_TURRET_WRAP = 1
# wait times in seconds for spindle brake, release
SPINDLE_OFF_WAIT          =     1.0
SPINDLE_ON_WAIT           =     1.5

[KINS]
KINEMATICS =  trivkins
JOINTS = 3

[AXIS_X]
MIN_LIMIT = -14.0
MAX_LIMIT = 14.0
MAX_VELOCITY = 5.0
MAX_ACCELERATION = 20.0

[JOINT_0]
TYPE =                          LINEAR
HOME =                          0.000
MAX_VELOCITY =                  5.0
MAX_ACCELERATION =              20.0
# Yes really, this .0024 backlash is right, workshop 2007
BACKLASH = 0.0024
# 2000 cycles/rev * 4 counts/cycle * 1.6 gear ratio *
#  1.0 cm pitch * 2.54 cm/in = 32512 (tweaked later)
INPUT_SCALE =                   33865   
OUTPUT_SCALE = 1.000
OUTPUT_OFFSET = -0.032
MAX_OUTPUT =  10.0
MIN_LIMIT =                     -14.0
MAX_LIMIT =                     14.0
FERROR = 0.050
MIN_FERROR = 0.010
HOME_OFFSET =                    14.3
HOME_SEARCH_VEL =                2.5
HOME_LATCH_VEL =                 -0.25
HOME_USE_INDEX =                 YES
HOME_IGNORE_LIMITS =             NO
HOME_SEQUENCE = 		 1
# PID tuning params
DEADBAND =  0.000035
PGAIN =     4000.0
IGAIN =     6400.0
DGAIN =       15.0
FF0 =       0.000
FF1 =       0.000
FF2 =				0.0
BIAS =      0.000
# Second axis

[AXIS_Y]
MIN_LIMIT = -7.7
MAX_LIMIT = 7.7
MAX_VELOCITY = 5.0
MAX_ACCELERATION = 20.0

[JOINT_1]
TYPE =                          LINEAR
HOME =                          0.000
MAX_VELOCITY =                  5.0
MAX_ACCELERATION =              20.0
BACKLASH = 0.000
# 2000 cycles/rev * 4 counts/cycle * 1.6 gear ratio *
#  1.0 cm pitch * 2.54 cm/in = 32512 (tweaked later)
INPUT_SCALE =                   33865 
OUTPUT_SCALE = 1.000
OUTPUT_OFFSET = 0.006
MAX_OUTPUT =  10.0
MIN_LIMIT =                     -7.7
MAX_LIMIT =                      7.7
FERROR = 0.050
MIN_FERROR = 0.010
HOME_OFFSET =                    7.37
HOME_SEARCH_VEL =                2.5
HOME_LATCH_VEL =                 -0.25
HOME_USE_INDEX =                 YES
HOME_IGNORE_LIMITS =             NO
HOME_SEQUENCE = 		 1
# PID tuning params
DEADBAND =  0.000035
PGAIN =     4000.0
IGAIN =     6400.0
DGAIN =       15.0
FF0 =       0.000
FF1 =       0.000
FF2 =				0.0
BIAS =      0.000
# Third axis

[AXIS_Z]
MIN_LIMIT = -7.75
MAX_LIMIT = 0.025
MAX_VELOCITY = 3.0
MAX_ACCELERATION = 20.0

[JOINT_2]
TYPE =                          LINEAR
HOME =                          0.0
MAX_VELOCITY =                  3.0
MAX_ACCELERATION =              20.0
BACKLASH = 0.000
# 2000 cycles/rev * 4 counts/cycle * 2.0 gear ratio *
#  1.0 cm pitch * 2.54 cm/in = 40640
# 2007 workshop: we found that the scale was 20% low
# and has apparently been that way for a couple years
# dunno whether the gear ratio is actually 2.5, or the
# screw is actually 8mm instead of 10mm, or something
# else, but the number below gives correct results
INPUT_SCALE =                   50800
OUTPUT_SCALE = 1.000
OUTPUT_OFFSET = -0.014
MAX_OUTPUT =  10.0
MIN_LIMIT =                     -7.75
MAX_LIMIT =                      0.025
FERROR = 0.050
MIN_FERROR = 0.010
HOME_OFFSET =                    -0.2
HOME_SEARCH_VEL =                1.0
HOME_LATCH_VEL =                 -0.25
HOME_USE_INDEX =                 YES
HOME_IGNORE_LIMITS =             NO
HOME_SEQUENCE = 		 0
# PID tuning params
DEADBAND =  0.00003
PGAIN =     4000.0
IGAIN =     6400.0
DGAIN =       15.0
FF0 =       0.000
FF1 =       0.000
FF2 =				0.0
BIAS =      0.000
# section for main IO controller parameters -----------------------------------

[SPINDLE]
ORIENT_POSITION =       -75.0
LOW_GEAR_RATIO =        4.133
HIGH_GEAR_RATIO =       1.100
# Trajectory planner section --------------------------------------------------

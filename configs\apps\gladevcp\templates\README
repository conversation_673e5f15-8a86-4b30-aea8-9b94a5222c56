These are example gladevcp handler classes to start your own.

------------------------------------------
To run independently:

$ gladevcp -u functionhandler.py button.ui
$ gladevcp -u classhandler.py button.ui
$ gladevcp -u classhandler_persistent.py  -U debug=2 -U "print 'debug=%d' % debug"  button.ui

NB: button.ui has really no interesting widget state to restore.

have a look at classhandler_persistent.ini to get an
idea how the persistent state is saved. It should look roughly like this:

# generated by gladevcp.util.create_default_ini() on Wed Dec  8 12:27:13 2010
[ini]
        version = 1
        signature = be44d45a0e07d57cfbe8915d668e8d3b399a4cdb
[vars]
        abool = True
        afloat = 1.67
        anint = 42
        astring = sometext
[widgets]
# last update  by gladevcp.util.save_state() on Wed Dec  8 12:31:20 2010



-<PERSON> 12/2010

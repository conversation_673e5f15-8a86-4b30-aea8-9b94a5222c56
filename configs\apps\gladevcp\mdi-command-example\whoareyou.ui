<?xml version="1.0"?>
<interface>
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-requires gladevcp 0.0 -->
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <child>
      <object class="GtkButton" id="button1">
        <property name="label" translatable="yes">Who are you?</property>
        <property name="visible">True</property>
        <property name="can_focus">True</property>
        <property name="receives_default">True</property>
        <property name="related_action">hal_action_mdi1</property>
      </object>
    </child>
  </object>
  <object class="EMC_Action_MDI" id="hal_action_mdi1">
    <property name="command">(MSG, "Hi, I'm an EMC_Action_MDI")</property>
  </object>
</interface>

<?xml version="1.0"?>
<interface>
  <requires lib="gtk+" version="2.16"/>
  <!-- interface-requires gladevcp 0.0 -->
  <!-- interface-naming-policy project-wide -->
  <object class="GtkWindow" id="window1">
    <signal name="destroy" handler="on_destroy"/>
    <child>
      <object class="GtkVBox" id="vbox1">
        <property name="visible">True</property>
        <property name="homogeneous">True</property>
        <child>
          <object class="HAL_Button" id="hal_button1">
            <property name="label" translatable="yes">not much here -
but press me anyway</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
            <signal name="pressed" handler="on_button_press"/>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="HAL_SpinButton" id="hal_spinbutton1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="invisible_char">&#x25CF;</property>
            <property name="adjustment">adjustment1</property>
            <property name="digits">1</property>
          </object>
          <packing>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="HAL_HScale" id="hal_hscale1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="adjustment">adjustment2</property>
          </object>
          <packing>
            <property name="position">2</property>
          </packing>
        </child>
        <child>
          <object class="GtkSpinButton" id="spinbutton1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="invisible_char">&#x25CF;</property>
            <property name="adjustment">adjustment3</property>
            <property name="digits">1</property>
          </object>
          <packing>
            <property name="position">3</property>
          </packing>
        </child>
        <child>
          <object class="GtkHScale" id="hscale1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="adjustment">adjustment4</property>
          </object>
          <packing>
            <property name="position">4</property>
          </packing>
        </child>
        <child>
          <object class="GtkToggleButton" id="togglebutton1">
            <property name="label" translatable="yes">togglebutton</property>
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="receives_default">True</property>
          </object>
          <packing>
            <property name="position">5</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="upper">100</property>
    <property name="step_increment">0.10000000000000001</property>
  </object>
  <object class="GtkAdjustment" id="adjustment2">
    <property name="upper">100</property>
    <property name="step_increment">0.10000000000000001</property>
  </object>
  <object class="GtkAdjustment" id="adjustment3">
    <property name="upper">100</property>
    <property name="step_increment">0.10000000000000001</property>
  </object>
  <object class="GtkAdjustment" id="adjustment4">
    <property name="upper">100</property>
    <property name="step_increment">0.10000000000000001</property>
  </object>
</interface>

[EMC]
# The version string for this INI file.
VERSION = 1.1
# Name of machine, for use with display, etc.
MACHINE =               HM2-Servo
# Debug level, 0 means no messages. See src/emc/nml_int/emcglb.h for others
#DEBUG =                0x00000003
#DEBUG =                0x00000007
DEBUG = 0

[DISPLAY]
# Name of display program, e.g., tklinuxcnc
DISPLAY =              axis
# Cycle time, in seconds, that display will sleep between polls
CYCLE_TIME =            0.0500
# Path to help file
HELP_FILE =             tklinuxcnc.txt
# Initial display setting for position, RELATIVE or MACHINE
POSITION_OFFSET =       RELATIVE
# Initial display setting for position, COMMANDED or ACTUAL
POSITION_FEEDBACK =     ACTUAL
# Highest value that will be allowed for feed override, 1.0 = 100%
MAX_FEED_OVERRIDE =     1.5
# Prefix to be used
PROGRAM_PREFIX = ../../nc_files/
# Introductory graphic
INTRO_GRAPHIC =         linuxcnc.gif
INTRO_TIME =            5

[FILTER]
PROGRAM_EXTENSION = .png,.gif,.jpg Grayscale Depth Image
PROGRAM_EXTENSION = .py Python Script
png = image-to-gcode
gif = image-to-gcode
jpg = image-to-gcode
py = python3

[RS274NGC]
# File containing interpreter variables
PARAMETER_FILE = hm2-servo.var

[EMCMOT]
EMCMOT =                motmod
# Timeout for comm to emcmot, in seconds
COMM_TIMEOUT =          1.0
# Servo task period, in nanoseconds
SERVO_PERIOD =          1000000

[TASK]
# Name of task controller program, e.g., milltask
TASK =                  milltask
# Cycle time, in seconds, that task controller will sleep between polls
CYCLE_TIME =            0.010

[HAL]
# The run script first uses halcmd to execute any HALFILE
# files, and then to execute any individual HALCMD commands.
# list of hal config files to run through halcmd
# files are executed in the order in which they appear
HALFILE = hm2-servo.hal
# list of halcmd commands to execute
# commands are executed in the order in which they appear
#HALCMD =               save neta

[HALUI]
#No Content

[TRAJ]
# COORDINATES =         X Y Z R P W
COORDINATES =           X Y Z
HOME =                  0 0 0 0
LINEAR_UNITS =          inch
ANGULAR_UNITS =         degree
DEFAULT_LINEAR_VELOCITY = 3.0
MAX_LINEAR_VELOCITY = 4.0
DEFAULT_LINEAR_ACCELERATION = 6.0
MAX_LINEAR_ACCELERATION = 7.0

[EMCIO]
# tool table file
TOOL_TABLE = tool.tbl

[KINS]
KINEMATICS = trivkins
JOINTS = 3

[AXIS_X]
MIN_LIMIT = -3.0
MAX_LIMIT = 10.0
MAX_VELOCITY = 1.0
MAX_ACCELERATION = 4.0

[JOINT_0]
TYPE =                  LINEAR
MAX_VELOCITY =          1.0
MAX_ACCELERATION =      4.0
BACKLASH =              0.000
FERROR =                0.010
MIN_FERROR =            0.002
INPUT_SCALE =           81920
OUTPUT_SCALE =          -1.000
OUTPUT_OFFSET =         0.0
MAX_OUTPUT =            1.0
MIN_LIMIT =             -3.0
MAX_LIMIT =             10.0
HOME =                  0.000
HOME_OFFSET =           -2.9
HOME_SEARCH_VEL =       -0.50
HOME_LATCH_VEL =        0.10
# the X axis servo's encoder does not have an index channel, so we have to home without index
HOME_USE_INDEX =        NO
HOME_IGNORE_LIMITS =    YES
HOME_SEQUENCE =         1
# PID tuning params
DEADBAND =              0.000015
P =                     100.0
I =                     0.000
D =                     0.000
FF0 =                   0.000
FF1 =                   1.000
FF2 =			0.0
BIAS =                  0.000

[AXIS_Y]
MIN_LIMIT = -3.0
MAX_LIMIT = 10.0
MAX_VELOCITY = 1.0
MAX_ACCELERATION = 4.0

[JOINT_1]
TYPE =                  LINEAR
MAX_VELOCITY =          1.0
MAX_ACCELERATION =      4.0
BACKLASH =              0.000
FERROR =                0.010
MIN_FERROR =            0.002
INPUT_SCALE =           -81920
OUTPUT_SCALE =          1.000
OUTPUT_OFFSET =         0.0
MAX_OUTPUT =            1.0
MIN_LIMIT =             -3.0
MAX_LIMIT =             10.0
HOME =                  0.000
HOME_OFFSET =           -2.9
HOME_SEARCH_VEL =       -0.50
HOME_LATCH_VEL =        0.10
# the Y axis servo's encoder has an index channel, so we use it to improve the home accuracy
HOME_USE_INDEX =        YES
HOME_IGNORE_LIMITS =    YES
HOME_SEQUENCE =         1
# PID tuning params
DEADBAND =              0.000015
P =                     100.0
I =                     0.000
D =                     0.000
FF0 =                   0.000
FF1 =                   1.000
FF2 =			0.0
BIAS =                  0.000

[AXIS_Z]
MIN_LIMIT = -3.0
MAX_LIMIT = 3.0
MAX_VELOCITY = 1.0
MAX_ACCELERATION = 4.0

[JOINT_2]
TYPE =                  LINEAR
MAX_VELOCITY =          1.0
MAX_ACCELERATION =      4.0
BACKLASH =              0.000
FERROR =                0.010
MIN_FERROR =            0.002
INPUT_SCALE =           81920
OUTPUT_SCALE =          -1.000
OUTPUT_OFFSET =         0.0
MAX_OUTPUT =            1.0
MIN_LIMIT =             -3.0
MAX_LIMIT =             3.0
HOME =                  0.0
HOME_OFFSET =           -2.9
HOME_SEARCH_VEL =       -0.50
HOME_LATCH_VEL =        0.10
# the Z axis servo's encoder does not have an index channel, so we have to home without index
HOME_USE_INDEX =        NO
HOME_IGNORE_LIMITS =    YES
HOME_SEQUENCE =         0
# PID tuning params
DEADBAND =              0.000015
P =                     100.0
I =                     0.000
D =                     0.000
FF0 =                   0.000
FF1 =                   1.000
FF2 =			0.0
BIAS =                  0.000

[HOSTMOT2]
DRIVER=hm2_pci
BOARD=5i22
CONFIG="firmware=hm2/5i22-1/SVST8_8.BIT num_encoders=3 num_pwmgens=3 num_stepgens=0"

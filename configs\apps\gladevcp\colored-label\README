How to colorize a HAL_Label widget depending on the pin value
-------------------------------------------------------------

Setting the background color of a GtkLabel widget (and HAL_Label is
derived from GtkLabel) is a bit tricky. The GtkLabel widget has no
window object of its own for performance reasons, and only window
objects can have a background color. The solution is to enclose the
Labl in an EventBox container, which has a window but is otherwise
invisible - see the coloredlabel.ui file.

To help with this, the colorize() method tries to detect this
situation and give a warning if the widget is a GtkLabel and the
parent widget is not an GtkEventBox. It can be used to set the colors
of arbitrary widgets, not just GtkLabel.

Also, this example shows how to set a custom text and color label
depending on the HAL pin's value.

------------------------------------------
To run independently:

$ gladevcp -u coloredlabel.py -H coloredlabel.hal coloredlabel.ui


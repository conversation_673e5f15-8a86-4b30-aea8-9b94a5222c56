# first load realtime portion of classicladder, and create a 1mS thread
loadrt classicladder_rt period=1000000

# hook functions to realtime thread
addf classicladder.0.refresh classicladder.thread 1

# create some I/O signals

# connect I/O signals to classicladder for Estop
# connect I/O signals to classicladder for THC

net I0 <= classicladder.0.in-00
net I1 <= classicladder.0.in-01
net I2 <= classicladder.0.in-02
net I3 <= classicladder.0.in-03
net I4 <= classicladder.0.in-04
net I5 <= classicladder.0.in-05
net I6 <= classicladder.0.in-06
net I7 <= classicladder.0.in-07
net I8 <= classicladder.0.in-08
net I9 <= classicladder.0.in-09
net I10 <= classicladder.0.in-10
net I11 <= classicladder.0.in-11
net I12 <= classicladder.0.in-12

net Q0 => classicladder.0.out-00
net Q1 => classicladder.0.out-01
net Q2 => classicladder.0.out-02
net Q3 <= classicladder.0.out-03
net Q4 <= classicladder.0.out-04
net Q5 <= classicladder.0.out-05
net Q6 <= classicladder.0.out-06
net Q7 <= classicladder.0.out-07
net Q8 <= classicladder.0.out-08


# set the input signals
sets I0 0
sets I1 0
sets I2 0

# set the intra_communication signals
sets A0 0

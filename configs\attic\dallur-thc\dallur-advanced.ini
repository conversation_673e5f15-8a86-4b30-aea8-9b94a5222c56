# EMC controller parameters for generic controller. Make these what you need
# for your system.
# General note: Comments can either be preceded with a # or ; - either is
# acceptable, although # is in keeping with most linux config files.
# General section -------------------------------------------------------------

[EMC]
# Version of this INI file
VERSION = 1.1
# Name of machine, for use with display, etc.
MACHINE =               LinuxCNC-DALLUR-ADVANCED
# Debug level, 0 means no messages. See src/emc/nml_int/emcglb.h for others
# DEBUG =                 0x00000001
# DEBUG =               0x00000007
# DEBUG =               0x7FFFFFFF
DEBUG = 0
# Enable Adaptive Feedrate for THC
# Enable adaptive feed input, we use it for feedhold button and to
# prevent motion during inverse-homing of torch and for pierce delay
RS274NGC_STARTUP_CODE = M52 P1
# Sections for display options ------------------------------------------------

[DISPLAY]
# Name of display program, e.g., xlinuxcnc
# DISPLAY =             axis
# DISPLAY =		xlinuxcnc
 DISPLAY = 		tklinuxcnc
# Cycle time, in seconds, that display will sleep between polls
CYCLE_TIME =            0.200
# Path to help file
HELP_FILE =             tklinuxcnc.txt
# Initial display setting for position, RELATIVE or MACHINE
POSITION_OFFSET =       RELATIVE
# Initial display setting for position, COMMANDED or ACTUAL
POSITION_FEEDBACK =     ACTUAL
# Highest value that will be allowed for feed override, 1.0 = 100%
MAX_FEED_OVERRIDE =     1.2
# Prefix to be used
PROGRAM_PREFIX = ../../nc_files/
# Introductory graphic
INTRO_GRAPHIC = linuxcnc.gif
INTRO_TIME = 2
# Increments for the JOG section
INCREMENTS = 1000 10 1 0.1 0.01
# Task controller section -----------------------------------------------------

[FILTER]
#No Content

[RS274NGC]
# File containing interpreter variables
PARAMETER_FILE =        dallur-advanced.var
# Motion control section ------------------------------------------------------

[EMCMOT]
EMCMOT =              motmod
# Timeout for comm to emcmot, in seconds
COMM_TIMEOUT =          1.0
# Base task period, in nanoseconds - this is the fastest thread in the machine
#BASE_PERIOD =		      10000
#BASE_PERIOD =		      14107
#BASE_PERIOD = 		      11000
#BASE_PERIOD =		      15000
BASE_PERIOD = 		      20000 
#BASE_PERIOD =                50000
# Servo task period, in nanoseconds - will be rounded to an integer multiple
#   of BASE_PERIOD
SERVO_PERIOD =               1000000
# Hardware Abstraction Layer section --------------------------------------------------

[TASK]
# Name of task controller program, e.g., milltask
TASK =                  milltask
# Cycle time, in seconds, that task controller will sleep between polls
CYCLE_TIME =            0.010
# Part program interpreter section --------------------------------------------

[HAL]
# The run script first uses halcmd to execute any HALFILE
# files, and then to execute any individual HALCMD commands.
#
# HALUI to interact with NML 
# Run before any other HALFILE
HALUI=halui 
# list of hal config files to run through halcmd
# files are executed in the order in which they appear
HALFILE =                    dallur-core_stepper.hal
HALFILE =                    dallur-advanced.hal
HALFILE =                    dallur-halvcp.hal
#HALFILE =                    xylotex_pinout.hal
# HALFILE =                    standard_pinout.hal
# HALFILE =                    simulated_limits.hal
# list of halcmd commands to execute
# commands are executed in the order in which they appear
#HALCMD =                    save neta
# Trajectory planner section --------------------------------------------------

[HALUI]
#No Content

[TRAJ]
COORDINATES =           X Y Z
HOME =                  0 0 0
LINEAR_UNITS = 		mm
ANGULAR_UNITS =         degree
DEFAULT_LINEAR_VELOCITY = 180.00
MAX_LINEAR_VELOCITY = 260.00
DEFAULT_LINEAR_ACCELERATION = 500.0
MAX_LINEAR_ACCELERATION = 2400.0
# Axes sections ---------------------------------------------------------------
# First axis

[EMCIO]
# tool table file
TOOL_TABLE =    dallur-advanced.tbl
# section for external NML server parameters ----------------------------------

[KINS]
KINEMATICS =  trivkins
JOINTS = 3

[AXIS_X]
MIN_LIMIT = 0
MAX_LIMIT = 2100
MAX_VELOCITY = 220
MAX_ACCELERATION = 2400

[JOINT_0]
TYPE =                          LINEAR
HOME =                          0.000
MAX_VELOCITY =                  220
MAX_ACCELERATION =              2400
STEPGEN_MAXACCEL =             	2800 
BACKLASH = 			0.000
SCALE =                   150.333 
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     0
MAX_LIMIT =                     2100
FERROR = 			1.270
MIN_FERROR = 			0.254
HOME_OFFSET =                   0.0
HOME_SEARCH_VEL =               0.0
HOME_LATCH_VEL =                0.0
HOME_USE_INDEX =                NO
HOME_IGNORE_LIMITS =            NO
# Second axis

[AXIS_Y]
MIN_LIMIT = 0.0
MAX_LIMIT = 6100
MAX_VELOCITY = 240
MAX_ACCELERATION = 2400

[JOINT_1]
TYPE =                          LINEAR
HOME =                          0.000
MAX_VELOCITY =                  240
MAX_ACCELERATION =              2400
STEPGEN_MAXACCEL =              2800
BACKLASH = 			0.000
SCALE =                   150.333 
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     0.0
MAX_LIMIT =                     6100
FERROR = 			0.127
MIN_FERROR = 			0.254
HOME_OFFSET =                   0.0
HOME_SEARCH_VEL =               0.0
HOME_LATCH_VEL =                0.0
HOME_USE_INDEX =                NO
HOME_IGNORE_LIMITS =            NO
# Third axis

[AXIS_Z]
MIN_LIMIT = 0.0
MAX_LIMIT = 4000.0
MAX_VELOCITY = 20
MAX_ACCELERATION = 250

[JOINT_2]
TYPE =                          LINEAR
HOME =                          0.0
MAX_VELOCITY =                  20
MAX_ACCELERATION =              250
STEPGEN_MAXACCEL =              300
BACKLASH = 			0.000
SCALE =                   150.333 
OUTPUT_SCALE = 			1.000
MIN_LIMIT =                     0.0
MAX_LIMIT =                     4000.0
FERROR = 			0.127
#MIN_FERROR = 			0.254
MIN_FERROR = 			500
HOME_OFFSET =                    0.0
HOME_SEARCH_VEL =                0.0
HOME_LATCH_VEL =                 0.0
HOME_USE_INDEX =                 NO
HOME_IGNORE_LIMITS =             NO
# Settings for the Torch Height Control which does not use the stepgen but depends on a PID loop
P =				2500
I =				0.0
D =				0.0
BIAS =				0.0
FF0 =				0.0
FF1 =				0.0
FF2 =				0.0
DEADBAND=			0.000375
# section for main IO controller parameters -----------------------------------

[EMCSERVER]
# Uncomment the following line if you need to run a remote GUI.
# EMCSERVER =           emcsvr

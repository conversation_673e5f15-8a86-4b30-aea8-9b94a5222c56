# core HAL config file for steppers

# first load the core RT modules that will be needed
# kinematics
loadrt [KINS]KINEMATICS
# motion controller, get name and thread periods from ini file
loadrt [EMCMOT]EMCMOT base_period_nsec=[EMCMOT]BASE_PERIOD servo_period_nsec=[EMCMOT]SERVO_PERIOD num_joints=[KINS]JOINTS
# stepper module, three step generators, all three using step/dir
loadrt stepgen step_type=0,0,0

# hook functions to base thread (high speed thread for step generation)
addf stepgen.make-pulses base-thread

# hook functions to servo thread
addf stepgen.capture-position servo-thread
addf motion-command-handler servo-thread
addf motion-controller servo-thread
addf stepgen.update-freq servo-thread

# connect position commands from motion module to step generator
net Xpos-cmd <= joint.0.motor-pos-cmd
net Xpos-cmd => stepgen.0.position-cmd
net Ypos-cmd <= joint.1.motor-pos-cmd
net Ypos-cmd => stepgen.1.position-cmd
# Disabled to make THC work better
#linksp Zpos-cmd <= joint.2.motor-pos-cmd
# Disabled for THC to work
#linksp Zpos-cmd => stepgen.2.position-cmd

# connect position feedback from step generators
# to motion module
net Xpos-fb <= stepgen.0.position-fb
net Xpos-fb => joint.0.motor-pos-fb
net Ypos-fb <= stepgen.1.position-fb
net Ypos-fb => joint.1.motor-pos-fb
net Zpos-fb <= stepgen.2.position-fb
net Zpos-fb => joint.2.motor-pos-fb

# connect enable signals for step generators
net Xen <= joint.0.amp-enable-out
net Xen => stepgen.0.enable
net Yen <= joint.1.amp-enable-out
net Yen => stepgen.1.enable
net Zen <= joint.2.amp-enable-out
net Zen => stepgen.2.enable


# connect signals to step pulse generator outputs
net Xstep stepgen.0.step
net Xdir stepgen.0.dir
net Ystep stepgen.1.step
net Ydir stepgen.1.dir
net Zstep stepgen.2.step
net Zdir stepgen.2.dir

# set stepgen module scaling - get values from ini file
setp stepgen.0.position-scale [JOINT_0]SCALE
setp stepgen.1.position-scale [JOINT_1]SCALE
setp stepgen.2.position-scale [JOINT_2]SCALE

# set stepgen module accel limits - get values from ini file
setp stepgen.0.maxaccel [JOINT_0]STEPGEN_MAXACCEL
setp stepgen.1.maxaccel [JOINT_1]STEPGEN_MAXACCEL
setp stepgen.2.maxaccel [JOINT_2]STEPGEN_MAXACCEL


<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.38.2 -->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <requires lib="gladevcp" version="0.0"/>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="upper">100</property>
    <property name="step-increment">1</property>
    <property name="page-increment">10</property>
    <property name="page-size">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment2">
    <property name="upper">100</property>
    <property name="step-increment">1</property>
    <property name="page-increment">10</property>
    <property name="page-size">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment3">
    <property name="upper">100</property>
    <property name="step-increment">1</property>
    <property name="page-increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment4">
    <property name="upper">1000</property>
    <property name="step-increment">1</property>
    <property name="page-increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment5">
    <property name="upper">100</property>
    <property name="step-increment">1</property>
    <property name="page-increment">10</property>
    <property name="page-size">10</property>
  </object>
  <object class="GtkWindow" id="window1">
    <property name="can-focus">False</property>
    <child>
      <object class="GtkHBox" id="hbox1">
        <property name="visible">True</property>
        <property name="can-focus">False</property>
        <child>
          <object class="HAL_Table" id="hal_table1">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <child>
              <object class="HAL_SpinButton" id="hal_spinbutton1">
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="invisible-char">●</property>
                <property name="adjustment">adjustment3</property>
              </object>
              <packing>
                <property name="left-attach">1</property>
                <property name="right-attach">2</property>
                <property name="top-attach">1</property>
                <property name="bottom-attach">2</property>
              </packing>
            </child>
            <child>
              <object class="HAL_HBox" id="hal_hbox1">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <child>
                  <object class="HAL_LED" id="hal_led1">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="led-blink-rate">0</property>
                    <property name="pick-color-off">#00002724ffff</property>
                    <property name="pick-color-on">#f88096020000</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="HAL_LED" id="hal_led2">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="led-blink-rate">0</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
                <child>
                  <object class="HAL_VBar" id="hal_vbar1">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="bg-color">#bebebebebebe</property>
                    <property name="force-width">30</property>
                    <property name="invert">True</property>
                    <property name="min">10</property>
                    <property name="z0-border">0.699999988079071</property>
                    <property name="z0-color">#0000ffff0000</property>
                    <property name="z1-border">0.8000000119209289</property>
                    <property name="z1-color">#ffffffff0000</property>
                    <property name="z2-color">#ffff00000000</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">2</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="left-attach">1</property>
                <property name="right-attach">2</property>
              </packing>
            </child>
            <child>
              <object class="HAL_VScale" id="hal_vscale1">
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="adjustment">adjustment2</property>
              </object>
              <packing>
                <property name="left-attach">2</property>
                <property name="right-attach">3</property>
              </packing>
            </child>
            <child>
              <object class="HAL_Label" id="hal_label1">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="label" translatable="yes">label</property>
                <property name="label-pin-type">1</property>
                <property name="text-template">%.02f</property>
              </object>
              <packing>
                <property name="left-attach">2</property>
                <property name="right-attach">3</property>
                <property name="top-attach">1</property>
                <property name="bottom-attach">2</property>
              </packing>
            </child>
            <child>
              <object class="HAL_Label" id="hal_label2">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="label" translatable="yes">label</property>
              </object>
              <packing>
                <property name="top-attach">1</property>
                <property name="bottom-attach">2</property>
              </packing>
            </child>
            <child>
              <object class="HAL_RadioButton" id="hal_radiobutton2">
                <property name="label" translatable="yes">radiobutton</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">False</property>
                <property name="draw-indicator">True</property>
                <property name="group">hal_radiobutton1</property>
              </object>
              <packing>
                <property name="left-attach">1</property>
                <property name="right-attach">2</property>
                <property name="top-attach">3</property>
                <property name="bottom-attach">4</property>
              </packing>
            </child>
            <child>
              <object class="HAL_RadioButton" id="hal_radiobutton1">
                <property name="label" translatable="yes">radiobutton</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">False</property>
                <property name="active">True</property>
                <property name="draw-indicator">True</property>
              </object>
              <packing>
                <property name="left-attach">1</property>
                <property name="right-attach">2</property>
                <property name="top-attach">2</property>
                <property name="bottom-attach">3</property>
              </packing>
            </child>
            <child>
              <object class="HAL_LED" id="hal_led4">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="led-blink-rate">0</property>
              </object>
              <packing>
                <property name="left-attach">2</property>
                <property name="right-attach">3</property>
                <property name="top-attach">2</property>
                <property name="bottom-attach">3</property>
              </packing>
            </child>
            <child>
              <object class="HAL_LED" id="hal_led5">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="led-blink-rate">500</property>
              </object>
              <packing>
                <property name="left-attach">2</property>
                <property name="right-attach">3</property>
                <property name="top-attach">3</property>
                <property name="bottom-attach">4</property>
              </packing>
            </child>
            <child>
              <object class="HAL_CheckButton" id="hal_checkbutton1">
                <property name="label" translatable="yes">checkbutton</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">False</property>
                <property name="draw-indicator">True</property>
              </object>
            </child>
            <child>
              <object class="HAL_HBar" id="hal_hbar1">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="bg-color">#bebebebebebe</property>
                <property name="force-height">20</property>
                <property name="max">50</property>
                <property name="z0-border">0.699999988079071</property>
                <property name="z0-color">#0000ffff0000</property>
                <property name="z1-border">0.8500000238418579</property>
                <property name="z1-color">#ffffffff0000</property>
                <property name="z2-color">#ffff00000000</property>
              </object>
              <packing>
                <property name="top-attach">2</property>
                <property name="bottom-attach">3</property>
              </packing>
            </child>
            <child>
              <object class="HAL_HScale" id="hal_hscale2">
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="adjustment">adjustment5</property>
              </object>
              <packing>
                <property name="top-attach">3</property>
                <property name="bottom-attach">4</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkVBox" id="vbox1">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <child>
              <object class="HAL_Button" id="hal_button1">
                <property name="label" translatable="yes">button</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">True</property>
              </object>
              <packing>
                <property name="expand">True</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="HAL_ToggleButton" id="hal_togglebutton1">
                <property name="label" translatable="yes">togglebutton</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">True</property>
              </object>
              <packing>
                <property name="expand">True</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <placeholder/>
            </child>
            <child>
              <object class="HAL_HScale" id="hal_hscale1">
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="adjustment">adjustment1</property>
              </object>
              <packing>
                <property name="expand">True</property>
                <property name="fill">True</property>
                <property name="position">3</property>
              </packing>
            </child>
            <child>
              <object class="GtkHBox" id="hbox2">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <child>
                  <object class="GtkLabel" id="label1">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="label" translatable="yes">Scale</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="HAL_SpinButton" id="hal_spinbutton2">
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="invisible-char">●</property>
                    <property name="adjustment">adjustment4</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">True</property>
                <property name="fill">True</property>
                <property name="position">4</property>
              </packing>
            </child>
            <child>
              <object class="HAL_ProgressBar" id="hal_progressbar1">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="scale">180</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">False</property>
                <property name="position">5</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
